/**
 * 激活码管理服务
 * 处理激活码管理相关的API调用
 */

import ApiClient from '@/lib/api-client';
import { ApiResponse } from '@/lib/api-utils';

// 激活码相关的类型定义
export interface ActivationCode {
  id: number;
  code: string;
  vip_level: 'v1' | 'v2' | 'v3' | 'v4';
  vip_duration_days: number;
  status: 'active' | 'used' | 'expired' | 'disabled';
  used_by?: number;
  used_at?: string;
  created_by: number;
  created_at: string;
  expires_at?: string;
  description?: string;
  batch_id?: string;
}

export interface ActivationCodeStats {
  total: number;
  active: number;
  used: number;
  expired: number;
  disabled: number;
  by_level: {
    v1: number;
    v2: number;
    v3: number;
    v4: number;
  };
}

export interface CreateActivationCodeRequest {
  vip_level: 'v1' | 'v2' | 'v3' | 'v4';
  vip_duration_days?: number;
  expires_at?: string;
  description?: string;
  count?: number;
}

export interface UpdateActivationCodeRequest {
  status: 'active' | 'disabled';
  description?: string;
}

export interface ActivationCodeListParams {
  page?: number;
  limit?: number;
  search?: string;
  vip_level?: string;
  status?: string;
  batch_id?: string;
  created_by?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ActivationCodeListResponse {
  codes: ActivationCode[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UseActivationCodeRequest {
  code: string;
}

export interface UseActivationCodeResponse {
  vip_level: string;
  vip_duration_days: number;
  message: string;
}

// VIP等级配置
export const VIP_LEVEL_CONFIG = {
  v1: {
    label: 'VIP V1',
    description: '基础VIP会员',
    default_duration: 30,
    color: '#10B981', // green
  },
  v2: {
    label: 'VIP V2', 
    description: '进阶VIP会员',
    default_duration: 60,
    color: '#3B82F6', // blue
  },
  v3: {
    label: 'VIP V3',
    description: '高级VIP会员', 
    default_duration: 90,
    color: '#8B5CF6', // purple
  },
  v4: {
    label: 'VIP V4',
    description: '至尊VIP会员',
    default_duration: 180,
    color: '#F59E0B', // amber
  },
} as const;

// 激活码管理服务类
export class ActivationCodeService {
  /**
   * 获取激活码列表
   */
  static async getActivationCodes(params?: ActivationCodeListParams): Promise<ApiResponse<ActivationCodeListResponse>> {
    return ApiClient.get<ActivationCodeListResponse>('/activation-codes', params);
  }

  /**
   * 根据ID获取激活码详情
   */
  static async getActivationCodeById(id: number): Promise<ApiResponse<ActivationCode>> {
    return ApiClient.get<ActivationCode>(`/activation-codes/${id}`);
  }

  /**
   * 创建激活码
   */
  static async createActivationCodes(data: CreateActivationCodeRequest): Promise<ApiResponse<ActivationCode[]>> {
    return ApiClient.post<ActivationCode[]>('/activation-codes', data);
  }

  /**
   * 更新激活码
   */
  static async updateActivationCode(id: number, data: UpdateActivationCodeRequest): Promise<ApiResponse<void>> {
    return ApiClient.patch<void>(`/activation-codes/${id}`, data);
  }

  /**
   * 删除激活码
   */
  static async deleteActivationCode(id: number): Promise<ApiResponse<void>> {
    return ApiClient.delete<void>(`/activation-codes/${id}`);
  }

  /**
   * 获取激活码统计信息
   */
  static async getActivationCodeStats(): Promise<ApiResponse<ActivationCodeStats>> {
    return ApiClient.get<ActivationCodeStats>('/activation-codes/stats');
  }

  /**
   * 使用激活码 (用户端)
   */
  static async useActivationCode(data: UseActivationCodeRequest): Promise<ApiResponse<UseActivationCodeResponse>> {
    return ApiClient.post<UseActivationCodeResponse>('/activation-codes/use', data);
  }

  /**
   * 获取VIP等级配置
   */
  static getVipLevelConfig() {
    return VIP_LEVEL_CONFIG;
  }

  /**
   * 获取VIP等级标签
   */
  static getVipLevelLabel(level: string): string {
    return VIP_LEVEL_CONFIG[level as keyof typeof VIP_LEVEL_CONFIG]?.label || level;
  }

  /**
   * 获取VIP等级颜色
   */
  static getVipLevelColor(level: string): string {
    return VIP_LEVEL_CONFIG[level as keyof typeof VIP_LEVEL_CONFIG]?.color || '#6B7280';
  }

  /**
   * 获取状态标签
   */
  static getStatusLabel(status: string): string {
    const statusLabels = {
      active: '有效',
      used: '已使用',
      expired: '已过期',
      disabled: '已禁用'
    };
    return statusLabels[status as keyof typeof statusLabels] || status;
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: string): string {
    const statusColors = {
      active: '#10B981', // green
      used: '#6B7280', // gray
      expired: '#EF4444', // red
      disabled: '#F59E0B' // amber
    };
    return statusColors[status as keyof typeof statusColors] || '#6B7280';
  }

  /**
   * 格式化激活码显示
   */
  static formatCode(code: string): string {
    // 确保激活码以 XXXX-XXXX-XXXX-XXXX 格式显示
    const cleanCode = code.replace(/[^A-Z0-9]/g, '');
    return cleanCode.replace(/(.{4})/g, '$1-').slice(0, -1);
  }

  /**
   * 验证激活码格式
   */
  static validateCodeFormat(code: string): boolean {
    const codePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    return codePattern.test(code.trim().toUpperCase());
  }
}

export default ActivationCodeService;
