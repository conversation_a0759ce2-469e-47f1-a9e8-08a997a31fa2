"use client"

import { useState, useEffect, useCallback } from 'react';
import { withAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

// Import our new components
import { UserStatsCards } from '@/components/users/user-stats-cards';
import { UserFilters } from '@/components/users/user-filters';
import { UserTable } from '@/components/users/user-table';
import { UserPagination } from '@/components/users/user-pagination';
import { BanUserDialog, SetVipDialog } from '@/components/users/user-action-dialogs';

// Import services and types
import { UserService, UserListParams, UserStats } from '@/services/user.service';
import { User } from '@/services/auth.service';

function UsersPage() {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats>({
    total: 0,
    active: 0,
    banned: 0,
    vip: 0,
    newThisMonth: 0,
  });
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // Filter state
  const [filters, setFilters] = useState<UserListParams>({
    page: 1,
    limit: 20,
  });

  // Dialog state
  const [banDialog, setBanDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  const [vipDialog, setVipDialog] = useState<{
    open: boolean;
    user: User | null;
  }>({ open: false, user: null });

  // Load users data
  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await UserService.getUsers(filters);

      if (response.success && response.data) {
        setUsers(response.data.users);
        setPagination({
          page: response.pagination?.page || 1,
          limit: response.pagination?.limit || 20,
          total: response.pagination?.total || 0,
          totalPages: response.pagination?.totalPages || 0,
        });
      } else {
        toast.error('加载用户列表失败');
      }
    } catch (error) {
      console.error('Failed to load users:', error);
      toast.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load user statistics
  const loadStats = useCallback(async () => {
    try {
      setStatsLoading(true);
      const response = await UserService.getUserStats();

      if (response.success && response.data) {
        setStats(response.data);
      } else {
        toast.error('加载统计数据失败');
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
      toast.error('加载统计数据失败');
    } finally {
      setStatsLoading(false);
    }
  }, []);

  // Event handlers
  const handleFiltersChange = (newFilters: UserListParams) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setFilters({ page: 1, limit: 20 });
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleRefresh = () => {
    loadUsers();
    loadStats();
  };

  // User action handlers
  const handleBanUser = async (reason: string, expiresAt?: Date) => {
    if (!banDialog.user) return;

    try {
      setActionLoading(true);
      const response = await UserService.banUser(banDialog.user.id, {
        reason,
        expiresAt: expiresAt?.toISOString(),
      });

      if (response.success) {
        toast.success('用户已封禁');
        setBanDialog({ open: false, user: null });
        loadUsers();
        loadStats();
      } else {
        toast.error(response.error || '封禁用户失败');
      }
    } catch (error) {
      console.error('Ban user error:', error);
      toast.error('封禁用户失败');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUnbanUser = async (user: User) => {
    try {
      setActionLoading(true);
      const response = await UserService.unbanUser(user.id);

      if (response.success) {
        toast.success('用户已解封');
        loadUsers();
        loadStats();
      } else {
        toast.error(response.error || '解封用户失败');
      }
    } catch (error) {
      console.error('Unban user error:', error);
      toast.error('解封用户失败');
    } finally {
      setActionLoading(false);
    }
  };

  const handleSetVip = async (level: string, expiresAt?: Date) => {
    if (!vipDialog.user) return;

    try {
      setActionLoading(true);
      const response = await UserService.setVipLevel(vipDialog.user.id, {
        level: level as any,
        expiresAt: expiresAt?.toISOString(),
      });

      if (response.success) {
        toast.success('VIP等级已设置');
        setVipDialog({ open: false, user: null });
        loadUsers();
        loadStats();
      } else {
        toast.error(response.error || '设置VIP等级失败');
      }
    } catch (error) {
      console.error('Set VIP error:', error);
      toast.error('设置VIP等级失败');
    } finally {
      setActionLoading(false);
    }
  };

  const handleViewUser = (user: User) => {
    // TODO: Implement user detail view
    toast.info('用户详情功能开发中');
  };

  const handleEditUser = (user: User) => {
    // TODO: Implement user edit
    toast.info('编辑用户功能开发中');
  };

  // Load data on mount and when filters change
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">用户管理</h2>
          <p className="text-muted-foreground">
            管理uniapp前端注册用户，包括搜索、封号解封、VIP等级管理等功能
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            添加用户
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <UserStatsCards stats={stats} loading={statsLoading} />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            管理所有注册用户，支持搜索、筛选和批量操作
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <UserFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onReset={handleFiltersReset}
          />

          {/* User Table */}
          <UserTable
            users={users}
            loading={loading}
            onBanUser={(user) => setBanDialog({ open: true, user })}
            onUnbanUser={handleUnbanUser}
            onSetVip={(user) => setVipDialog({ open: true, user })}
            onViewUser={handleViewUser}
            onEditUser={handleEditUser}
          />

          {/* Pagination */}
          {pagination.total > 0 && (
            <UserPagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              pageSize={pagination.limit}
              total={pagination.total}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <BanUserDialog
        open={banDialog.open}
        onOpenChange={(open) => setBanDialog({ open, user: banDialog.user })}
        user={banDialog.user}
        onConfirm={handleBanUser}
        loading={actionLoading}
      />

      <SetVipDialog
        open={vipDialog.open}
        onOpenChange={(open) => setVipDialog({ open, user: vipDialog.user })}
        user={vipDialog.user}
        onConfirm={handleSetVip}
        loading={actionLoading}
      />
    </div>
  );
}

export default withAuth(UsersPage);
