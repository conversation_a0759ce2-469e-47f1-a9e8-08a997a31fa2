import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import CryptoJS from 'crypto-js';
import { db } from './database';

// Types
export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  role: 'admin' | 'user' | 'moderator';
  avatar_url?: string;
  is_active: boolean;
  is_banned: boolean;
  ban_reason?: string;
  ban_expires_at?: Date;
  banned_by?: number;
  banned_at?: Date;
  vip_level: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  vip_expires_at?: Date;
  registration_source: 'web' | 'mobile' | 'api' | 'admin';
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  birth_date?: Date;
  last_login?: Date;
  login_count: number;
  created_at: Date;
  updated_at: Date;
}

export interface Session {
  id: string;
  user_id: number;
  expires_at: Date;
  created_at: Date;
}

export interface JWTPayload {
  userId: number;
  username: string;
  email: string;
  role: string;
  sessionId: string;
}

// Constants
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const SALT_ROUNDS = 12;
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days

// Password utilities
export class PasswordUtils {
  static async hash(password: string): Promise<string> {
    return bcrypt.hash(password, SALT_ROUNDS);
  }

  static async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static generateSecurePassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }
}

// JWT utilities
export class JWTUtils {
  static sign(payload: Omit<JWTPayload, 'sessionId'> & { sessionId?: string }): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
  }

  static verify(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as JWTPayload;
    } catch (error) {
      return null;
    }
  }

  static decode(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      return null;
    }
  }
}

// Session management
export class SessionManager {
  static generateSessionId(): string {
    return CryptoJS.lib.WordArray.random(32).toString();
  }

  static async createSession(userId: number): Promise<string> {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + SESSION_DURATION);

    await db.query(
      'INSERT INTO sessions (id, user_id, expires_at) VALUES (?, ?, ?)',
      [sessionId, userId, expiresAt]
    );

    return sessionId;
  }

  static async getSession(sessionId: string): Promise<Session | null> {
    return db.queryOne<Session>(
      'SELECT * FROM sessions WHERE id = ? AND expires_at > NOW()',
      [sessionId]
    );
  }

  static async deleteSession(sessionId: string): Promise<void> {
    await db.query('DELETE FROM sessions WHERE id = ?', [sessionId]);
  }

  static async deleteUserSessions(userId: number): Promise<void> {
    await db.query('DELETE FROM sessions WHERE user_id = ?', [userId]);
  }

  static async cleanExpiredSessions(): Promise<void> {
    await db.query('DELETE FROM sessions WHERE expires_at <= NOW()');
  }
}

// User management
export class UserManager {
  static async createUser(userData: {
    username: string;
    email: string;
    password: string;
    full_name?: string;
    role?: 'admin' | 'user' | 'moderator';
    registration_source?: 'web' | 'mobile' | 'api' | 'admin';
    phone?: string;
    gender?: 'male' | 'female' | 'other';
    birth_date?: Date;
  }): Promise<User> {
    const passwordHash = await PasswordUtils.hash(userData.password);

    const result = await db.query(
      `INSERT INTO users (username, email, password_hash, full_name, role, registration_source, phone, gender, birth_date)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userData.username,
        userData.email,
        passwordHash,
        userData.full_name || null,
        userData.role || 'user',
        userData.registration_source || 'web',
        userData.phone || null,
        userData.gender || null,
        userData.birth_date || null
      ]
    ) as unknown as { insertId: number };

    const user = await this.getUserById(result.insertId);
    if (!user) {
      throw new Error('Failed to create user');
    }

    return user;
  }

  static async getUserById(id: number): Promise<User | null> {
    return db.queryOne<User>(
      'SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    return db.queryOne<User>(
      'SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE email = ?',
      [email]
    );
  }

  static async getUserByUsername(username: string): Promise<User | null> {
    return db.queryOne<User>(
      'SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE username = ?',
      [username]
    );
  }

  static async authenticateUser(emailOrUsername: string, password: string): Promise<User | null> {
    const user = await db.queryOne<User & { password_hash: string }>(
      'SELECT * FROM users WHERE (email = ? OR username = ?) AND is_active = true',
      [emailOrUsername, emailOrUsername]
    );

    if (!user) {
      return null;
    }

    const isValidPassword = await PasswordUtils.verify(password, user.password_hash);
    if (!isValidPassword) {
      return null;
    }

    // Update last login and login count
    await db.query(
      'UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?',
      [user.id]
    );

    // Remove password hash from returned user
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  // Ban/Unban user methods
  static async banUser(userId: number, reason: string, bannedBy: number, expiresAt?: Date): Promise<void> {
    await db.query(
      `UPDATE users SET
       is_banned = true,
       ban_reason = ?,
       banned_by = ?,
       banned_at = NOW(),
       ban_expires_at = ?
       WHERE id = ?`,
      [reason, bannedBy, expiresAt || null, userId]
    );
  }

  static async unbanUser(userId: number): Promise<void> {
    await db.query(
      `UPDATE users SET
       is_banned = false,
       ban_reason = NULL,
       banned_by = NULL,
       banned_at = NULL,
       ban_expires_at = NULL
       WHERE id = ?`,
      [userId]
    );
  }

  // VIP management methods
  static async setVipLevel(userId: number, level: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond', expiresAt?: Date): Promise<void> {
    await db.query(
      'UPDATE users SET vip_level = ?, vip_expires_at = ? WHERE id = ?',
      [level, expiresAt || null, userId]
    );
  }

  // Get users with filters and pagination
  static async getUsersWithFilters(params: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    vip_level?: string;
    is_active?: boolean;
    is_banned?: boolean;
    registration_source?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<{ users: User[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      vip_level,
      is_active,
      is_banned,
      registration_source,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const values: any[] = [];

    // Build WHERE conditions
    if (search) {
      conditions.push('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)');
      const searchTerm = `%${search}%`;
      values.push(searchTerm, searchTerm, searchTerm);
    }

    if (role) {
      conditions.push('role = ?');
      values.push(role);
    }

    if (vip_level) {
      conditions.push('vip_level = ?');
      values.push(vip_level);
    }

    if (typeof is_active === 'boolean') {
      conditions.push('is_active = ?');
      values.push(is_active);
    }

    if (typeof is_banned === 'boolean') {
      conditions.push('is_banned = ?');
      values.push(is_banned);
    }

    if (registration_source) {
      conditions.push('registration_source = ?');
      values.push(registration_source);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await db.queryOne<{ total: number }>(countQuery, values);
    const total = countResult?.total || 0;

    // Get users - use string interpolation for LIMIT/OFFSET to avoid parameter issues
    const usersQuery = `
      SELECT id, username, email, full_name, role, avatar_url, is_active, is_banned,
             ban_reason, ban_expires_at, banned_by, banned_at, vip_level, vip_expires_at,
             registration_source, phone, gender, birth_date, last_login, login_count,
             created_at, updated_at
      FROM users
      ${whereClause}
      ${orderClause}
      LIMIT ${limit} OFFSET ${offset}
    `;

    const users = await db.query<User>(usersQuery, values);

    return { users, total };
  }

  // Get user statistics
  static async getUserStats(): Promise<{
    total: number;
    active: number;
    banned: number;
    vip: number;
    newThisMonth: number;
  }> {
    const stats = await db.queryOne<{
      total: number;
      active: number;
      banned: number;
      vip: number;
      newThisMonth: number;
    }>(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_banned = true THEN 1 ELSE 0 END) as banned,
        SUM(CASE WHEN vip_level != 'none' THEN 1 ELSE 0 END) as vip,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END) as newThisMonth
      FROM users
    `);

    return stats || { total: 0, active: 0, banned: 0, vip: 0, newThisMonth: 0 };
  }

  static async updateUser(id: number, updates: Partial<User>): Promise<User | null> {
    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);

    await db.query(
      `UPDATE users SET ${setClause}, updated_at = NOW() WHERE id = ?`,
      [...values, id]
    );

    return this.getUserById(id);
  }

  static async deleteUser(id: number): Promise<void> {
    await db.query('DELETE FROM users WHERE id = ?', [id]);
  }

  static async getAllUsers(page: number = 1, limit: number = 10): Promise<{ users: User[], total: number }> {
    const offset = (page - 1) * limit;
    
    const users = await db.query<User>(
      `SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at 
       FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    const [{ total }] = await db.query<{ total: number }>('SELECT COUNT(*) as total FROM users');

    return { users, total };
  }
}

// Authentication middleware helper
export async function verifyAuth(token: string): Promise<{ user: User; session: Session } | null> {
  try {
    const payload = JWTUtils.verify(token);
    if (!payload) {
      return null;
    }

    const [user, session] = await Promise.all([
      UserManager.getUserById(payload.userId),
      SessionManager.getSession(payload.sessionId)
    ]);

    if (!user || !session || !user.is_active) {
      return null;
    }

    return { user, session };
  } catch (error) {
    return null;
  }
}
