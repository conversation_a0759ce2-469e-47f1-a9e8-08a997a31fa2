"use client"

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Copy, 
  Edit, 
  Trash2, 
  Eye, 
  Ban, 
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { ActivationCode, ActivationCodeService } from '@/services/activation-code.service';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface ActivationCodeTableProps {
  codes: ActivationCode[];
  loading?: boolean;
  onEdit: (code: ActivationCode) => void;
  onDelete: (code: ActivationCode) => void;
  onView: (code: ActivationCode) => void;
  onStatusChange: (code: ActivationCode, status: 'active' | 'disabled') => void;
}

export function ActivationCodeTable({
  codes,
  loading,
  onEdit,
  onDelete,
  onView,
  onStatusChange
}: ActivationCodeTableProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      toast.success('激活码已复制到剪贴板');
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { 
        label: '有效', 
        variant: 'default' as const, 
        icon: CheckCircle,
        color: 'text-green-600'
      },
      used: { 
        label: '已使用', 
        variant: 'secondary' as const, 
        icon: XCircle,
        color: 'text-gray-600'
      },
      expired: { 
        label: '已过期', 
        variant: 'destructive' as const, 
        icon: Clock,
        color: 'text-red-600'
      },
      disabled: { 
        label: '已禁用', 
        variant: 'outline' as const, 
        icon: Ban,
        color: 'text-orange-600'
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {config.label}
      </Badge>
    );
  };

  const getVipLevelBadge = (level: string) => {
    const config = ActivationCodeService.getVipLevelConfig()[level as keyof typeof ActivationCodeService.getVipLevelConfig()];
    
    if (!config) {
      return <Badge variant="outline">{level}</Badge>;
    }

    return (
      <Badge 
        variant="outline" 
        className="gap-1"
        style={{ 
          borderColor: config.color,
          color: config.color 
        }}
      >
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { 
      addSuffix: true, 
      locale: zhCN 
    });
  };

  if (loading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>激活码</TableHead>
              <TableHead>VIP等级</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>有效期</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>使用情况</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                </TableCell>
                <TableCell>
                  <div className="h-8 bg-gray-200 rounded animate-pulse w-8"></div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (codes.length === 0) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>激活码</TableHead>
              <TableHead>VIP等级</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>有效期</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>使用情况</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center text-muted-foreground">
                暂无激活码数据
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>激活码</TableHead>
            <TableHead>VIP等级</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>有效期</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>使用情况</TableHead>
            <TableHead className="w-[100px]">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {codes.map((code) => (
            <TableRow key={code.id}>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                    {ActivationCodeService.formatCode(code.code)}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyCode(code.code)}
                    className="h-6 w-6 p-0"
                  >
                    <Copy className={`h-3 w-3 ${copiedCode === code.code ? 'text-green-600' : ''}`} />
                  </Button>
                </div>
              </TableCell>
              <TableCell>{getVipLevelBadge(code.vip_level)}</TableCell>
              <TableCell>{getStatusBadge(code.status)}</TableCell>
              <TableCell className="text-sm">
                {code.vip_duration_days} 天
                {code.expires_at && (
                  <div className="text-xs text-muted-foreground">
                    过期: {formatDate(code.expires_at)}
                  </div>
                )}
              </TableCell>
              <TableCell className="text-sm">
                {formatDate(code.created_at)}
              </TableCell>
              <TableCell className="text-sm">
                {code.status === 'used' && code.used_at ? (
                  <div>
                    <div className="text-green-600">已使用</div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(code.used_at)}
                    </div>
                  </div>
                ) : (
                  <span className="text-muted-foreground">未使用</span>
                )}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => onView(code)}>
                      <Eye className="mr-2 h-4 w-4" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCopyCode(code.code)}>
                      <Copy className="mr-2 h-4 w-4" />
                      复制激活码
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {code.status !== 'used' && (
                      <>
                        <DropdownMenuItem onClick={() => onEdit(code)}>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        {code.status === 'active' ? (
                          <DropdownMenuItem onClick={() => onStatusChange(code, 'disabled')}>
                            <Ban className="mr-2 h-4 w-4" />
                            禁用
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => onStatusChange(code, 'active')}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            启用
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete(code)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
